# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=modern_lms
DB_USERNAME=lms_user
DB_PASSWORD=lms_password
MYSQL_ROOT_PASSWORD=rootpassword

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=lms_user
RABBITMQ_PASSWORD=lms_password
RABBITMQ_VHOST=/

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# JWT Configuration
JWT_SECRET=mySecretKey123456789012345678901234567890
JWT_EXPIRATION=86400000

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Mail Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# File Upload Configuration
FILE_UPLOAD_DIR=./uploads
CERTIFICATE_STORAGE_PATH=./certificates
CERTIFICATE_OUTPUT_DIR=./certificates

# SSL Configuration
SSL_ENABLED=false
SSL_KEY_STORE=classpath:keystore.p12
SSL_KEY_STORE_PASSWORD=password
SSL_KEY_STORE_TYPE=PKCS12
SSL_KEY_ALIAS=lms

# Monitoring Configuration
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=200h

# Application Configuration
APP_NAME=Modern LMS
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_BASE_URL=http://localhost:8080

# Security Configuration
SECURITY_REQUIRE_SSL=false
SECURITY_ENABLE_CSRF=false
SECURITY_SESSION_TIMEOUT=3600

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/application.log
LOG_MAX_FILE_SIZE=10MB
LOG_MAX_HISTORY=30

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Feature Flags
FEATURE_EMAIL_VERIFICATION=true
FEATURE_TWO_FACTOR_AUTH=false
FEATURE_SOCIAL_LOGIN=true
FEATURE_COURSE_RECOMMENDATIONS=true
FEATURE_ANALYTICS=true

# External Services
PAYMENT_GATEWAY_URL=https://api.stripe.com
PAYMENT_GATEWAY_KEY=sk_test_...
VIDEO_STREAMING_URL=https://api.vimeo.com
VIDEO_STREAMING_KEY=your-vimeo-key

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=lms-backups
BACKUP_S3_REGION=us-east-1

# Development/Testing
DEV_MODE=true
TEST_DATABASE_URL=jdbc:h2:mem:testdb
MOCK_EXTERNAL_SERVICES=false
