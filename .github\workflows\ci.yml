name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  JAVA_VERSION: '17'

jobs:
  # Backend Tests
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: rootpassword
          MYSQL_DATABASE: test_lms
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_password
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      rabbitmq:
        image: rabbitmq:3.12-management
        env:
          RABBITMQ_DEFAULT_USER: test_user
          RABBITMQ_DEFAULT_PASS: test_password
        ports:
          - 5672:5672
          - 15672:15672
        options: >-
          --health-cmd="rabbitmq-diagnostics -q ping"
          --health-interval=30s
          --health-timeout=30s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Wait for MySQL
      run: |
        echo "Waiting for MySQL to be ready..."
        for i in {1..30}; do
          if mysqladmin ping -h"127.0.0.1" -P3306 -utest_user -ptest_password --silent; then
            echo "MySQL is ready!"
            break
          fi
          echo "Waiting for MySQL... ($i/30)"
          sleep 10
        done

    - name: Wait for RabbitMQ
      run: |
        echo "Waiting for RabbitMQ to be ready..."
        for i in {1..30}; do
          if curl -f http://localhost:15672/api/overview -u test_user:test_password; then
            echo "RabbitMQ is ready!"
            break
          fi
          echo "Waiting for RabbitMQ... ($i/30)"
          sleep 10
        done

    - name: Verify database connection
      run: |
        cd backend
        echo "Testing database connection..."
        mysql -h"127.0.0.1" -P3306 -utest_user -ptest_password -e "SELECT 'Database connection successful!' as status;"

    - name: Run backend tests
      run: |
        cd backend
        echo "Running backend tests with MySQL database..."
        mvn clean test -Dspring.profiles.active=test -Dmaven.test.failure.ignore=false
      env:
        DB_USERNAME: test_user
        DB_PASSWORD: test_password
        RABBITMQ_HOST: localhost
        RABBITMQ_USERNAME: test_user
        RABBITMQ_PASSWORD: test_password
        HIBERNATE_DIALECT: org.hibernate.dialect.MySQLDialect

    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Backend Test Results
        path: backend/target/surefire-reports/*.xml
        reporter: java-junit

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: backend/target/site/jacoco/jacoco.xml
        flags: backend
        name: backend-coverage

  # Frontend Tests
  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run unit tests
      run: npm run test:coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # E2E Tests
  e2e-test:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: rootpassword
          MYSQL_DATABASE: test_lms
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_password
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      rabbitmq:
        image: rabbitmq:3.12-management
        env:
          RABBITMQ_DEFAULT_USER: test_user
          RABBITMQ_DEFAULT_PASS: test_password
        ports:
          - 5672:5672
        options: >-
          --health-cmd="rabbitmq-diagnostics -q ping"
          --health-interval=30s
          --health-timeout=30s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Build frontend
      run: npm run build

    - name: Start backend
      run: |
        cd backend
        mvn spring-boot:run -Dspring-boot.run.profiles=test &
        echo $! > backend.pid
      env:
        DB_USERNAME: test_user
        DB_PASSWORD: test_password
        RABBITMQ_HOST: localhost
        RABBITMQ_USERNAME: test_user
        RABBITMQ_PASSWORD: test_password

    - name: Wait for backend
      run: |
        timeout 120 bash -c 'until curl -f http://localhost:8080/api/health; do sleep 5; done'

    - name: Start frontend
      run: |
        npm run preview &
        echo $! > frontend.pid

    - name: Wait for frontend
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:5173; do sleep 2; done'

    - name: Run E2E tests
      run: npm run test:e2e

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

    - name: Stop services
      if: always()
      run: |
        if [ -f backend.pid ]; then kill $(cat backend.pid) || true; fi
        if [ -f frontend.pid ]; then kill $(cat frontend.pid) || true; fi

  # Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high
