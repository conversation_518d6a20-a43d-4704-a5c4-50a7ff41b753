<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
    <!-- 
    This file contains suppressions for known false positives in dependency vulnerability scanning.
    Each suppression should include:
    - A clear reason for suppression
    - The CVE or vulnerability being suppressed
    - An expiration date for review
    -->
    
    <!-- Example suppression for a false positive -->
    <!--
    <suppress>
        <notes><![CDATA[
        This vulnerability affects a different component than what we're using.
        The vulnerable functionality is not used in our application.
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.example/vulnerable\-lib@.*$</packageUrl>
        <cve>CVE-2023-12345</cve>
        <until>2024-12-31</until>
    </suppress>
    -->
    
    <!-- Suppress test-only dependencies in production -->
    <suppress>
        <notes><![CDATA[
        Test dependencies are not included in production builds.
        These vulnerabilities do not affect the runtime application.
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-boot\-starter\-test@.*$</packageUrl>
        <until>2025-12-31</until>
    </suppress>
    
    <suppress>
        <notes><![CDATA[
        H2 database is only used for testing, not in production.
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.h2database/h2@.*$</packageUrl>
        <until>2025-12-31</until>
    </suppress>
    
    <!-- Suppress development-only tools -->
    <suppress>
        <notes><![CDATA[
        Development tools are not included in production builds.
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot\-devtools@.*$</packageUrl>
        <until>2025-12-31</until>
    </suppress>
</suppressions>
