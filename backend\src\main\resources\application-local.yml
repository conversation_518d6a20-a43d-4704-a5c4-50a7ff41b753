# Local development profile (without Docker)
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: modern-lms-backend-local
  
  # H2 Database for local testing (no MySQL required)
  datasource:
    url: jdbc:h2:mem:lmsdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # H2 Console (for database inspection)
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  flyway:
    enabled: false  # Disable for H2 in-memory
  
  # Disable mail for local testing
  mail:
    host: localhost
    port: 2525
    username: test
    password: test
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false
  
  # Disable RabbitMQ for local testing
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

# JWT Configuration
jwt:
  secret: localTestSecretKey123456789012345678901234567890
  expiration: 86400000

# Logging
logging:
  level:
    com.lms: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# CORS Configuration
cors:
  allowed-origins: http://localhost:5173,http://localhost:3000
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
