apiVersion: v1
kind: ConfigMap
metadata:
  name: lms-config
  namespace: lms-system
data:
  # Database Configuration
  DB_HOST: "mysql-service"
  DB_PORT: "3306"
  DB_NAME: "modern_lms"
  
  # RabbitMQ Configuration
  RABBITMQ_HOST: "rabbitmq-service"
  RABBITMQ_PORT: "5672"
  RABBITMQ_VHOST: "/"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # Mail Configuration
  MAIL_HOST: "smtp.gmail.com"
  MAIL_PORT: "587"
  
  # Application Configuration
  SPRING_PROFILES_ACTIVE: "prod"
  CORS_ALLOWED_ORIGINS: "https://lms.example.com"
  
  # File Upload Configuration
  FILE_UPLOAD_DIR: "/app/uploads"
  CERTIFICATE_STORAGE_PATH: "/app/certificates"
  
  # Logging Configuration
  LOGGING_LEVEL_ROOT: "INFO"
  LOGGING_LEVEL_COM_LMS: "DEBUG"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: lms-system
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log warn;
        
        # Performance
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        server {
            listen 80;
            server_name _;
            root /usr/share/nginx/html;
            index index.html index.htm;
            
            # Handle client-side routing
            location / {
                try_files $uri $uri/ /index.html;
            }
            
            # API proxy to backend
            location /api/ {
                proxy_pass http://backend-service:8080;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Static assets caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # Error pages
            error_page 404 /index.html;
            error_page 500 502 503 504 /50x.html;
            location = /50x.html {
                root /usr/share/nginx/html;
            }
        }
    }
