apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-uploads-pvc
  namespace: lms-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-certificates-pvc
  namespace: lms-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: lms-system
  labels:
    app: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: ghcr.io/your-org/lms-backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: SPRING_PROFILES_ACTIVE
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: DB_NAME
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: DB_PASSWORD
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: RABBITMQ_HOST
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: RABBITMQ_PORT
        - name: RABBITMQ_USERNAME
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: RABBITMQ_USERNAME
        - name: RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: RABBITMQ_PASSWORD
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: REDIS_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: JWT_SECRET
        - name: CLOUDINARY_CLOUD_NAME
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: CLOUDINARY_CLOUD_NAME
        - name: CLOUDINARY_API_KEY
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: CLOUDINARY_API_KEY
        - name: CLOUDINARY_API_SECRET
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: CLOUDINARY_API_SECRET
        - name: MAIL_HOST
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: MAIL_HOST
        - name: MAIL_PORT
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: MAIL_PORT
        - name: MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: MAIL_USERNAME
        - name: MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: lms-secrets
              key: MAIL_PASSWORD
        - name: FILE_UPLOAD_DIR
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: FILE_UPLOAD_DIR
        - name: CERTIFICATE_STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: lms-config
              key: CERTIFICATE_STORAGE_PATH
        volumeMounts:
        - name: uploads-storage
          mountPath: /app/uploads
        - name: certificates-storage
          mountPath: /app/certificates
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: uploads-storage
        persistentVolumeClaim:
          claimName: backend-uploads-pvc
      - name: certificates-storage
        persistentVolumeClaim:
          claimName: backend-certificates-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: lms-system
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: lms-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
