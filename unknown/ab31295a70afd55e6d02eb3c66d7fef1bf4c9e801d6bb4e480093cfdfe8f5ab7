apiVersion: v1
kind: Secret
metadata:
  name: lms-secrets
  namespace: lms-system
type: Opaque
data:
  # Database credentials (base64 encoded)
  DB_USERNAME: bG1zX3VzZXI=  # lms_user
  DB_PASSWORD: bG1zX3Bhc3N3b3Jk  # lms_password
  MYSQL_ROOT_PASSWORD: cm9vdHBhc3N3b3Jk  # rootpassword
  
  # RabbitMQ credentials
  RABBITMQ_USERNAME: bG1zX3VzZXI=  # lms_user
  RABBITMQ_PASSWORD: bG1zX3Bhc3N3b3Jk  # lms_password
  
  # Redis password
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmQ=  # redis_password
  
  # JWT Secret
  JWT_SECRET: bXlTZWNyZXRLZXkxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTA=  # mySecretKey123456789012345678901234567890
  
  # Cloudinary credentials
  CLOUDINARY_CLOUD_NAME: Y2xvdWRfbmFtZQ==  # cloud_name
  CLOUDINARY_API_KEY: YXBpX2tleQ==  # api_key
  CLOUDINARY_API_SECRET: YXBpX3NlY3JldA==  # api_secret
  
  # Mail credentials
  MAIL_USERNAME: bWFpbEB1c2VybmFtZQ==  # mail@username
  MAIL_PASSWORD: bWFpbF9wYXNzd29yZA==  # mail_password

---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: lms-system
type: kubernetes.io/tls
data:
  # TLS certificate and key (base64 encoded)
  # Replace with your actual certificate and key
  tls.crt: LS0tLS1CRUdJTi...  # Your TLS certificate
  tls.key: LS0tLS1CRUdJTi...  # Your TLS private key
