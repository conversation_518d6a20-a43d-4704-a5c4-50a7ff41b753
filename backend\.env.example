# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=modern_lms
DB_USERNAME=lms_user
DB_PASSWORD=lms_password

# Test Database Configuration
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_NAME=test_lms
TEST_DB_USERNAME=test_user
TEST_DB_PASSWORD=test_password

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=lms_user
RABBITMQ_PASSWORD=lms_password
RABBITMQ_VHOST=/

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=mySecretKey123456789012345678901234567890

# Mail Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# File Upload Configuration
FILE_UPLOAD_DIR=./uploads
CERTIFICATE_STORAGE_PATH=./certificates
CERTIFICATE_BASE_URL=http://localhost:8080/api/certificates

# Spring Profile
SPRING_PROFILES_ACTIVE=local
