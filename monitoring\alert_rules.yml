groups:
- name: lms-alerts
  rules:
  # High CPU Usage
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

  # High Memory Usage
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

  # Disk Space Low
  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Disk space is running low"
      description: "Disk space is below 10% on {{ $labels.instance }} filesystem {{ $labels.mountpoint }}"

  # Application Down
  - alert: ApplicationDown
    expr: up{job="lms-backend"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "LMS Backend is down"
      description: "LMS Backend has been down for more than 1 minute"

  # Database Connection Issues
  - alert: DatabaseConnectionHigh
    expr: hikaricp_connections_active / hikaricp_connections_max > 0.8
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High database connection usage"
      description: "Database connection pool usage is above 80%"

  # High Response Time
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is above 2 seconds"

  # High Error Rate
  - alert: HighErrorRate
    expr: rate(http_server_requests_total{status=~"5.."}[5m]) / rate(http_server_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is above 5% for more than 5 minutes"

  # RabbitMQ Queue Size
  - alert: RabbitMQQueueHigh
    expr: rabbitmq_queue_messages > 1000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "RabbitMQ queue size is high"
      description: "Queue {{ $labels.queue }} has more than 1000 messages"

  # MySQL Slow Queries
  - alert: MySQLSlowQueries
    expr: rate(mysql_global_status_slow_queries[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "MySQL slow queries detected"
      description: "MySQL slow query rate is above 0.1 queries per second"

  # Redis Memory Usage
  - alert: RedisMemoryHigh
    expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Redis memory usage is high"
      description: "Redis memory usage is above 90%"

  # SSL Certificate Expiry
  - alert: SSLCertificateExpiry
    expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
    for: 1h
    labels:
      severity: warning
    annotations:
      summary: "SSL certificate expiring soon"
      description: "SSL certificate for {{ $labels.instance }} expires in less than 30 days"

  # Pod Restart Frequency
  - alert: PodRestartingFrequently
    expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Pod is restarting frequently"
      description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"
