server:
  port: 0

spring:
  application:
    name: modern-lms-backend-test

  datasource:
    # Use MySQL for CI/CD pipeline (matches GitHub Actions services)
    url: *******************************************************************************************************************************
    username: ${DB_USERNAME:test_user}
    password: ${DB_PASSWORD:test_password}
    driver-class-name: com.mysql.cj.jdbc.Driver

    # Fallback to H2 for local testing when MySQL is not available
    hikari:
      connection-test-query: SELECT 1
      connection-timeout: 20000
      maximum-pool-size: 5
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: ${HIBERNATE_DIALECT:org.hibernate.dialect.MySQLDialect}
        format_sql: true
        hbm2ddl:
          import_files_sql_extractor: org.hibernate.tool.hbm2ddl.MultipleLinesSqlCommandExtractor
  
  flyway:
    enabled: false  # Disabled for tests - using JPA create-drop
  
  mail:
    host: localhost
    port: 2525
    username: test
    password: test
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false
  
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:test_user}
    password: ${RABBITMQ_PASSWORD:test_password}
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT Configuration for testing
jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 3600000 # 1 hour for tests

# Cloudinary Configuration for testing
cloudinary:
  cloud-name: test-cloud
  api-key: test-key
  api-secret: test-secret

# Logging for tests
logging:
  level:
    com.lms: DEBUG
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.testcontainers: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# CORS Configuration for tests
cors:
  allowed-origins: http://localhost:3000,http://localhost:5173
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# File Upload Configuration for tests
file:
  upload-dir: ./test-uploads

# Certificate Configuration for tests
app:
  certificate:
    storage-path: ./test-certificates
    template: test-certificate-template
    base-url: http://localhost:8080/api/certificates

# Test-specific configurations
test:
  containers:
    mysql:
      image: mysql:8.0
      database: test_lms
      username: test_user
      password: test_password
    rabbitmq:
      image: rabbitmq:3.12-management
      username: test_user
      password: test_password
