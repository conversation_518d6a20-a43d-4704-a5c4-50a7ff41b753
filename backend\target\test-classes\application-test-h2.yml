# H2 Test Profile - For local testing without MySQL
server:
  port: 0

spring:
  application:
    name: modern-lms-backend-test-h2
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  flyway:
    enabled: false  # Disabled for H2 tests
  
  mail:
    host: localhost
    port: 2525
    username: test
    password: test
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false
  
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT Configuration for testing
jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 3600000 # 1 hour for tests

# Cloudinary Configuration for tests
cloudinary:
  cloud-name: test-cloud
  api-key: test-key
  api-secret: test-secret

# CORS Configuration for tests
cors:
  allowed-origins: http://localhost:3000,http://localhost:5173
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# File Upload Configuration for tests
file:
  upload-dir: ./test-uploads

# Certificate Configuration for tests
app:
  certificate:
    storage-path: ./test-certificates
    template: test-certificate-template
    base-url: http://localhost:8080/api/certificates

# Logging for tests
logging:
  level:
    com.lms: DEBUG
    org.springframework.security: WARN
    org.hibernate.SQL: DEBUG
    org.springframework.web: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
